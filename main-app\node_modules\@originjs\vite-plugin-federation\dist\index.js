"use strict";
const path$1 = require("path");
const MagicString = require("magic-string");
const fs = require("fs");
require("crypto");
function _interopNamespaceDefault(e) {
  const n = Object.create(null, { [Symbol.toStringTag]: { value: "Module" } });
  if (e) {
    for (const k in e) {
      if (k !== "default") {
        const d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: () => e[k]
        });
      }
    }
  }
  n.default = e;
  return Object.freeze(n);
}
const path__namespace = /* @__PURE__ */ _interopNamespaceDefault(path$1);
const PREFIX = `\0virtual:`;
function virtual(modules) {
  const resolvedIds = /* @__PURE__ */ new Map();
  Object.keys(modules).forEach((id) => {
    resolvedIds.set(path__namespace.resolve(id), modules[id]);
  });
  return {
    name: "virtual",
    resolveId(id, importer) {
      if (id in modules)
        return PREFIX + id;
      if (importer) {
        const importerNoPrefix = importer.startsWith(PREFIX) ? importer.slice(PREFIX.length) : importer;
        const resolved = path__namespace.resolve(path__namespace.dirname(importerNoPrefix), id);
        if (resolvedIds.has(resolved))
          return PREFIX + resolved;
      }
      return null;
    },
    load(id) {
      if (id.startsWith(PREFIX)) {
        const idNoPrefix = id.slice(PREFIX.length);
        return idNoPrefix in modules ? modules[idNoPrefix] : resolvedIds.get(idNoPrefix);
      }
      return null;
    }
  };
}
class WalkerBase {
  constructor() {
    this.should_skip = false;
    this.should_remove = false;
    this.replacement = null;
    this.context = {
      skip: () => this.should_skip = true,
      remove: () => this.should_remove = true,
      replace: (node) => this.replacement = node
    };
  }
  replace(parent, prop, index, node) {
    if (parent) {
      if (index !== null) {
        parent[prop][index] = node;
      } else {
        parent[prop] = node;
      }
    }
  }
  remove(parent, prop, index) {
    if (parent) {
      if (index !== null) {
        parent[prop].splice(index, 1);
      } else {
        delete parent[prop];
      }
    }
  }
}
class SyncWalker extends WalkerBase {
  constructor(enter, leave) {
    super();
    this.enter = enter;
    this.leave = leave;
  }
  visit(node, parent, prop, index) {
    if (node) {
      if (this.enter) {
        const _should_skip = this.should_skip;
        const _should_remove = this.should_remove;
        const _replacement = this.replacement;
        this.should_skip = false;
        this.should_remove = false;
        this.replacement = null;
        this.enter.call(this.context, node, parent, prop, index);
        if (this.replacement) {
          node = this.replacement;
          this.replace(parent, prop, index, node);
        }
        if (this.should_remove) {
          this.remove(parent, prop, index);
        }
        const skipped = this.should_skip;
        const removed = this.should_remove;
        this.should_skip = _should_skip;
        this.should_remove = _should_remove;
        this.replacement = _replacement;
        if (skipped)
          return node;
        if (removed)
          return null;
      }
      for (const key in node) {
        const value = node[key];
        if (typeof value !== "object") {
          continue;
        } else if (Array.isArray(value)) {
          for (let i = 0; i < value.length; i += 1) {
            if (value[i] !== null && typeof value[i].type === "string") {
              if (!this.visit(value[i], node, key, i)) {
                i--;
              }
            }
          }
        } else if (value !== null && typeof value.type === "string") {
          this.visit(value, node, key, null);
        }
      }
      if (this.leave) {
        const _replacement = this.replacement;
        const _should_remove = this.should_remove;
        this.replacement = null;
        this.should_remove = false;
        this.leave.call(this.context, node, parent, prop, index);
        if (this.replacement) {
          node = this.replacement;
          this.replace(parent, prop, index, node);
        }
        if (this.should_remove) {
          this.remove(parent, prop, index);
        }
        const removed = this.should_remove;
        this.replacement = _replacement;
        this.should_remove = _should_remove;
        if (removed)
          return null;
      }
    }
    return node;
  }
}
function walk(ast, { enter, leave }) {
  const instance = new SyncWalker(enter, leave);
  return instance.visit(ast, null);
}
const path = {};
const EXPOSES_MAP = /* @__PURE__ */ new Map();
const EXPOSES_KEY_MAP = /* @__PURE__ */ new Map();
const SHARED = "shared";
const DYNAMIC_LOADING_CSS = "dynamicLoadingCss";
const DYNAMIC_LOADING_CSS_PREFIX = "__v__css__";
const DEFAULT_ENTRY_FILENAME = "remoteEntry.js";
const builderInfo = {
  builder: "rollup",
  version: "",
  assetsDir: "",
  isHost: false,
  isRemote: false,
  isShared: false
};
const parsedOptions = {
  prodExpose: [],
  prodRemote: [],
  prodShared: [],
  devShared: [],
  devExpose: [],
  devRemote: []
};
const devRemotes = [];
const prodRemotes = [];
const viteConfigResolved = {
  config: void 0
};
const unaryTags = /* @__PURE__ */ new Set(["link", "meta", "base"]);
function serializeTag({ tag, attrs, children }, indent = "") {
  if (unaryTags.has(tag)) {
    return `<${tag}${serializeAttrs(attrs)}>`;
  } else {
    return `<${tag}${serializeAttrs(attrs)}>${serializeTags(
      children,
      incrementIndent(indent)
    )}</${tag}>`;
  }
}
function serializeTags(tags, indent = "") {
  if (typeof tags === "string") {
    return tags;
  } else if (tags && tags.length) {
    return tags.map((tag) => `${indent}${serializeTag(tag, indent)}
`).join("");
  }
  return "";
}
function serializeAttrs(attrs) {
  let res = "";
  for (const key in attrs) {
    if (typeof attrs[key] === "boolean") {
      res += attrs[key] ? ` ${key}` : ``;
    } else {
      res += ` ${key}="${escapeHtml(attrs[key])}"`;
    }
  }
  return res;
}
function incrementIndent(indent = "") {
  return `${indent}${indent[0] === "	" ? "	" : "  "}`;
}
const matchHtmlRegExp = /["'&<>]/;
function escapeHtml(string) {
  const str = "" + string;
  const match = matchHtmlRegExp.exec(str);
  if (!match) {
    return str;
  }
  let escape;
  let html = "";
  let index = 0;
  let lastIndex = 0;
  for (index = match.index; index < str.length; index++) {
    switch (str.charCodeAt(index)) {
      case 34:
        escape = "&quot;";
        break;
      case 38:
        escape = "&amp;";
        break;
      case 39:
        escape = "&#39;";
        break;
      case 60:
        escape = "&lt;";
        break;
      case 62:
        escape = "&gt;";
        break;
      default:
        continue;
    }
    if (lastIndex !== index) {
      html += str.substring(lastIndex, index);
    }
    lastIndex = index + 1;
    html += escape;
  }
  return lastIndex !== index ? html + str.substring(lastIndex, index) : html;
}
const headInjectRE = /([ \t]*)<\/head>/i;
const headPrependInjectRE = /([ \t]*)<head[^>]*>/i;
const htmlPrependInjectRE = /([ \t]*)<html[^>]*>/i;
const bodyPrependInjectRE = /([ \t]*)<body[^>]*>/i;
const doctypePrependInjectRE = /<!doctype html>/i;
const toPreloadTag = (href) => ({
  tag: "link",
  attrs: {
    rel: "modulepreload",
    crossorigin: true,
    href
  }
});
function injectToHead(html, tags, prepend = false) {
  if (tags.length === 0)
    return html;
  if (prepend) {
    if (headPrependInjectRE.test(html)) {
      return html.replace(
        headPrependInjectRE,
        (match, p1) => `${match}
${serializeTags(tags, incrementIndent(p1))}`
      );
    }
  } else {
    if (headInjectRE.test(html)) {
      return html.replace(
        headInjectRE,
        (match, p1) => `${serializeTags(tags, incrementIndent(p1))}${match}`
      );
    }
    if (bodyPrependInjectRE.test(html)) {
      return html.replace(
        bodyPrependInjectRE,
        (match, p1) => `${serializeTags(tags, p1)}
${match}`
      );
    }
  }
  return prependInjectFallback(html, tags);
}
function prependInjectFallback(html, tags) {
  if (htmlPrependInjectRE.test(html)) {
    return html.replace(htmlPrependInjectRE, `$&
${serializeTags(tags)}`);
  }
  if (doctypePrependInjectRE.test(html)) {
    return html.replace(doctypePrependInjectRE, `$&
${serializeTags(tags)}`);
  }
  return serializeTags(tags) + html;
}
function parseSharedOptions(options) {
  return parseOptions(
    options.shared || {},
    (value, key) => ({
      import: true,
      shareScope: "default",
      packagePath: key,
      manuallyPackagePathSetting: false,
      generate: true,
      modulePreload: false
    }),
    (value, key) => {
      value.import = value.import ?? true;
      value.shareScope = value.shareScope || "default";
      value.packagePath = value.packagePath || key;
      value.manuallyPackagePathSetting = value.packagePath !== key;
      value.generate = value.generate ?? true;
      value.modulePreload = value.modulePreload ?? false;
      return value;
    }
  );
}
function parseExposeOptions(options) {
  return parseOptions(
    options.exposes,
    (item) => {
      return {
        import: item,
        name: void 0,
        dontAppendStylesToHead: false
      };
    },
    (item) => ({
      import: item.import,
      name: item.name || void 0,
      dontAppendStylesToHead: item.dontAppendStylesToHead || false
    })
  );
}
function parseRemoteOptions(options) {
  return parseOptions(
    options.remotes ? options.remotes : {},
    (item) => ({
      external: Array.isArray(item) ? item : [item],
      shareScope: options.shareScope || "default",
      format: "esm",
      from: "vite",
      externalType: "url"
    }),
    (item) => ({
      external: Array.isArray(item.external) ? item.external : [item.external],
      shareScope: item.shareScope || options.shareScope || "default",
      format: item.format || "esm",
      from: item.from ?? "vite",
      externalType: item.externalType || "url"
    })
  );
}
function parseOptions(options, normalizeSimple, normalizeOptions) {
  if (!options) {
    return [];
  }
  const list = [];
  const array = (items) => {
    for (const item of items) {
      if (typeof item === "string") {
        list.push([item, normalizeSimple(item, item)]);
      } else if (item && typeof item === "object") {
        object(item);
      } else {
        throw new Error("Unexpected options format");
      }
    }
  };
  const object = (obj) => {
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === "string" || Array.isArray(value)) {
        list.push([key, normalizeSimple(value, key)]);
      } else {
        list.push([key, normalizeOptions(value, key)]);
      }
    }
  };
  if (Array.isArray(options)) {
    array(options);
  } else if (typeof options === "object") {
    object(options);
  } else {
    throw new Error("Unexpected options format");
  }
  return list;
}
const letterReg = new RegExp("[0-9a-zA-Z]+");
function removeNonRegLetter(str, reg = letterReg) {
  let needUpperCase = false;
  let ret = "";
  for (const c of str) {
    if (reg.test(c)) {
      ret += needUpperCase ? c.toUpperCase() : c;
      needUpperCase = false;
    } else {
      needUpperCase = true;
    }
  }
  return ret;
}
function getModuleMarker(value, type) {
  return type ? `__rf_${type}__${value}` : `__rf_placeholder__${value}`;
}
function normalizePath(id) {
  return path$1.posix.normalize(id.replace(/\\/g, "/"));
}
function createRemotesMap(remotes) {
  const createUrl = (remote) => {
    const external = remote.config.external[0];
    const externalType = remote.config.externalType;
    if (externalType === "promise") {
      return `()=>${external}`;
    } else {
      return `'${external}'`;
    }
  };
  return `const remotesMap = {
${remotes.map(
    (remote) => `'${remote.id}':{url:${createUrl(remote)},format:'${remote.config.format}',from:'${remote.config.from}'}`
  ).join(",\n  ")}
};`;
}
function getFileExtname(url) {
  const fileNameAndParamArr = normalizePath(url).split("/");
  const fileNameAndParam = fileNameAndParamArr[fileNameAndParamArr.length - 1];
  const fileName = fileNameAndParam.split("?")[0];
  return path$1.extname(fileName);
}
const REMOTE_FROM_PARAMETER = "remoteFrom";
const NAME_CHAR_REG = new RegExp("[0-9a-zA-Z@_-]+");
function joinUrlSegments(a, b) {
  if (!a || !b) {
    return a || b || "";
  }
  if (a[a.length - 1] === "/") {
    a = a.substring(0, a.length - 1);
  }
  if (b[0] !== "/") {
    b = "/" + b;
  }
  return a + b;
}
function toOutputFilePathWithoutRuntime(filename, type, hostId, hostType, config, toRelative) {
  const { renderBuiltUrl } = config.experimental;
  let relative = config.base === "" || config.base === "./";
  if (renderBuiltUrl) {
    const result = renderBuiltUrl(filename, {
      hostId,
      hostType,
      type,
      ssr: !!config.build.ssr
    });
    if (typeof result === "object") {
      if (result.runtime) {
        throw new Error(
          `{ runtime: "${result.runtime}" } is not supported for assets in ${hostType} files: ${filename}`
        );
      }
      if (typeof result.relative === "boolean") {
        relative = result.relative;
      }
    } else if (result) {
      return result;
    }
  }
  if (relative && !config.build.ssr) {
    return toRelative(filename, hostId);
  } else {
    return joinUrlSegments(config.base, filename);
  }
}
function prodRemotePlugin(options) {
  parsedOptions.prodRemote = parseRemoteOptions(options);
  for (const item of parsedOptions.prodRemote) {
    prodRemotes.push({
      id: item[0],
      regexp: new RegExp(`^${item[0]}/.+?`),
      config: item[1]
    });
  }
  const shareScope = options.shareScope || "default";
  let resolvedConfig;
  return {
    name: "originjs:remote-production",
    virtualFile: options.remotes ? {
      __federation__: `
                ${createRemotesMap(prodRemotes)}
                const currentImports = {}
                const loadJS = async (url, fn) => {
                    const resolvedUrl = typeof url === 'function' ? await url() : url;
                    const script = document.createElement('script')
                    script.type = 'text/javascript';
                    script.onload = fn;
                    script.src = resolvedUrl;
                    document.getElementsByTagName('head')[0].appendChild(script);
                }

                function get(name, ${REMOTE_FROM_PARAMETER}) {
                    return __federation_import(name).then(module => () => {
                        if (${REMOTE_FROM_PARAMETER} === 'webpack') {
                            return Object.prototype.toString.call(module).indexOf('Module') > -1 && module.default ? module.default : module
                        }
                        return module
                    })
                }
                
                function merge(obj1, obj2) {
                  const mergedObj = Object.assign(obj1, obj2);
                  for (const key of Object.keys(mergedObj)) {
                    if (typeof mergedObj[key] === 'object' && typeof obj2[key] === 'object') {
                      mergedObj[key] = merge(mergedObj[key], obj2[key]);
                    }
                  }
                  return mergedObj;
                }

                const wrapShareModule = ${REMOTE_FROM_PARAMETER} => {
                  return merge({
                    ${getModuleMarker("shareScope")}
                  }, (globalThis.__federation_shared__ || {})['${shareScope}'] || {});
                }

                async function __federation_import(name) {
                    currentImports[name] ??= import(name)
                    return currentImports[name]
                }

                const initMap = Object.create(null);

                async function __federation_method_ensure(remoteId) {
                    const remote = remotesMap[remoteId];
                    if (!remote.inited) {
                        if ('var' === remote.format) {
                            // loading js with script tag
                            return new Promise(resolve => {
                                const callback = () => {
                                    if (!remote.inited) {
                                        remote.lib = window[remoteId];
                                        remote.lib.init(wrapShareModule(remote.from))
                                        remote.inited = true;
                                    }
                                    resolve(remote.lib);
                                }
                                return loadJS(remote.url, callback);
                            });
                        } else if (['esm', 'systemjs'].includes(remote.format)) {
                            // loading js with import(...)
                            return new Promise((resolve, reject) => {
                                const getUrl = typeof remote.url === 'function' ? remote.url : () => Promise.resolve(remote.url);
                                getUrl().then(url => {
                                    import(/* @vite-ignore */ url).then(lib => {
                                        if (!remote.inited) {
                                            const shareScope = wrapShareModule(remote.from);
                                            lib.init(shareScope);
                                            remote.lib = lib;
                                            remote.lib.init(shareScope);
                                            remote.inited = true;
                                        }
                                        resolve(remote.lib);
                                    }).catch(reject)
                                })
                            })
                        }
                    } else {
                        return remote.lib;
                    }
                }

                function __federation_method_unwrapDefault(module) {
                    return (module?.__esModule || module?.[Symbol.toStringTag] === 'Module') ? module.default : module
                }

                function __federation_method_wrapDefault(module, need) {
                    if (!module?.default && need) {
                        let obj = Object.create(null);
                        obj.default = module;
                        obj.__esModule = true;
                        return obj;
                    }
                    return module;
                }

                function __federation_method_getRemote(remoteName, componentName) {
                    return __federation_method_ensure(remoteName).then((remote) => remote.get(componentName).then(factory => factory()));
                }

                function __federation_method_setRemote(remoteName, remoteConfig) {
                  remotesMap[remoteName] = remoteConfig;
                }

                export {
                    __federation_method_ensure,
                    __federation_method_getRemote,
                    __federation_method_setRemote,
                    __federation_method_unwrapDefault,
                    __federation_method_wrapDefault
                }
            `
    } : { __federation__: "" },
    configResolved(config) {
      resolvedConfig = config;
    },
    async transform(code, id) {
      if (builderInfo.isShared) {
        for (const sharedInfo of parsedOptions.prodShared) {
          if (!sharedInfo[1].emitFile) {
            sharedInfo[1].emitFile = this.emitFile({
              type: "chunk",
              id: sharedInfo[1].id ?? sharedInfo[1].packagePath,
              preserveSignature: "strict",
              name: `__federation_shared_${sharedInfo[0]}`
            });
          }
        }
        if (id === "\0virtual:__federation_fn_import") {
          const moduleMapCode = parsedOptions.prodShared.filter((shareInfo) => shareInfo[1].generate).map(
            (sharedInfo) => `'${sharedInfo[0]}':{get:()=>()=>__federation_import(import.meta.ROLLUP_FILE_URL_${sharedInfo[1].emitFile}),import:${sharedInfo[1].import}${sharedInfo[1].requiredVersion ? `,requiredVersion:'${sharedInfo[1].requiredVersion}'` : ""}}`
          ).join(",");
          return code.replace(
            getModuleMarker("moduleMap", "var"),
            `{${moduleMapCode}}`
          );
        }
      }
      if (builderInfo.isRemote) {
        for (const expose of parsedOptions.prodExpose) {
          if (!expose[1].emitFile) {
            expose[1].emitFile = this.emitFile({
              type: "chunk",
              id: expose[1].id ?? expose[1].import,
              name: EXPOSES_KEY_MAP.get(expose[0]),
              preserveSignature: "allow-extension"
            });
          }
        }
      }
      if (builderInfo.isHost) {
        if (id === "\0virtual:__federation__") {
          const res = [];
          parsedOptions.prodShared.forEach((arr) => {
            const obj = arr[1];
            let str = "";
            if (typeof obj === "object") {
              const fileUrl = `import.meta.ROLLUP_FILE_URL_${obj.emitFile}`;
              str += `get:()=>get(${fileUrl}, ${REMOTE_FROM_PARAMETER}), loaded:1`;
              res.push(`'${arr[0]}':{'${obj.version}':{${str}}}`);
            }
          });
          return code.replace(getModuleMarker("shareScope"), res.join(","));
        }
      }
      if (builderInfo.isHost || builderInfo.isShared) {
        let ast = null;
        try {
          ast = this.parse(code);
        } catch (err) {
          console.error(err);
        }
        if (!ast) {
          return null;
        }
        const magicString = new MagicString(code);
        const hasStaticImported = /* @__PURE__ */ new Map();
        let requiresRuntime = false;
        let hasImportShared = false;
        let modify = false;
        let manualRequired = null;
        walk(ast, {
          enter(node) {
            var _a, _b, _c, _d, _e;
            if (node.type === "ImportDeclaration") {
              const moduleName = node.source.value;
              if (parsedOptions.prodShared.some(
                (sharedInfo) => sharedInfo[0] === moduleName
              )) {
                const namedImportDeclaration = [];
                let defaultImportDeclaration = null;
                if (!((_a = node.specifiers) == null ? void 0 : _a.length)) {
                  magicString.remove(node.start, node.end);
                  modify = true;
                } else {
                  node.specifiers.forEach((specify) => {
                    var _a2;
                    if ((_a2 = specify.imported) == null ? void 0 : _a2.name) {
                      namedImportDeclaration.push(
                        `${specify.imported.name === specify.local.name ? specify.imported.name : `${specify.imported.name}:${specify.local.name}`}`
                      );
                    } else {
                      defaultImportDeclaration = specify.local.name;
                    }
                  });
                  hasImportShared = true;
                  if (defaultImportDeclaration && namedImportDeclaration.length) {
                    const imports = namedImportDeclaration.join(",");
                    const line = `const ${defaultImportDeclaration} = await importShared('${moduleName}');
const {${imports}} = ${defaultImportDeclaration};
`;
                    magicString.overwrite(node.start, node.end, line);
                  } else if (defaultImportDeclaration) {
                    magicString.overwrite(
                      node.start,
                      node.end,
                      `const ${defaultImportDeclaration} = await importShared('${moduleName}');
`
                    );
                  } else if (namedImportDeclaration.length) {
                    magicString.overwrite(
                      node.start,
                      node.end,
                      `const {${namedImportDeclaration.join(
                        ","
                      )}} = await importShared('${moduleName}');
`
                    );
                  }
                }
              }
            }
            if (node.type === "ImportDeclaration" && ((_b = node.source) == null ? void 0 : _b.value) === "virtual:__federation__") {
              manualRequired = node;
            }
            if ((node.type === "ImportExpression" || node.type === "ImportDeclaration" || node.type === "ExportNamedDeclaration") && ((_d = (_c = node.source) == null ? void 0 : _c.value) == null ? void 0 : _d.indexOf("/")) > -1) {
              const moduleId = node.source.value;
              const remote = prodRemotes.find((r) => r.regexp.test(moduleId));
              const needWrap = (remote == null ? void 0 : remote.config.from) === "vite";
              if (remote) {
                requiresRuntime = true;
                const modName = `.${moduleId.slice(remote.id.length)}`;
                switch (node.type) {
                  case "ImportExpression": {
                    magicString.overwrite(
                      node.start,
                      node.end,
                      `__federation_method_getRemote(${JSON.stringify(
                        remote.id
                      )} , ${JSON.stringify(
                        modName
                      )}).then(module=>__federation_method_wrapDefault(module, ${needWrap}))`
                    );
                    break;
                  }
                  case "ImportDeclaration": {
                    if ((_e = node.specifiers) == null ? void 0 : _e.length) {
                      const afterImportName = `__federation_var_${moduleId.replace(
                        /[@/\\.-]/g,
                        ""
                      )}`;
                      if (!hasStaticImported.has(moduleId)) {
                        hasStaticImported.set(moduleId, afterImportName);
                        magicString.overwrite(
                          node.start,
                          node.end,
                          `const ${afterImportName} = await __federation_method_getRemote(${JSON.stringify(
                            remote.id
                          )} , ${JSON.stringify(modName)});`
                        );
                      }
                      let deconstructStr = "";
                      node.specifiers.forEach((spec) => {
                        if (spec.type === "ImportDefaultSpecifier") {
                          magicString.appendRight(
                            node.end,
                            `
 let ${spec.local.name} = __federation_method_unwrapDefault(${afterImportName}) `
                          );
                        } else if (spec.type === "ImportSpecifier") {
                          const importedName = spec.imported.name;
                          const localName = spec.local.name;
                          deconstructStr += `${importedName === localName ? localName : `${importedName} : ${localName}`},`;
                        } else if (spec.type === "ImportNamespaceSpecifier") {
                          magicString.appendRight(
                            node.end,
                            `let {${spec.local.name}} = ${afterImportName}`
                          );
                        }
                      });
                      if (deconstructStr.length > 0) {
                        magicString.appendRight(
                          node.end,
                          `
 let {${deconstructStr.slice(
                            0,
                            -1
                          )}} = ${afterImportName}`
                        );
                      }
                    }
                    break;
                  }
                  case "ExportNamedDeclaration": {
                    const afterImportName = `__federation_var_${moduleId.replace(
                      /[@/\\.-]/g,
                      ""
                    )}`;
                    if (!hasStaticImported.has(moduleId)) {
                      hasStaticImported.set(moduleId, afterImportName);
                      magicString.overwrite(
                        node.start,
                        node.end,
                        `const ${afterImportName} = await __federation_method_getRemote(${JSON.stringify(
                          remote.id
                        )} , ${JSON.stringify(modName)});`
                      );
                    }
                    if (node.specifiers.length > 0) {
                      const specifiers = node.specifiers;
                      let exportContent = "";
                      let deconstructContent = "";
                      specifiers.forEach((spec) => {
                        const localName = spec.local.name;
                        const exportName = spec.exported.name;
                        const variableName = `${afterImportName}_${localName}`;
                        deconstructContent = deconstructContent.concat(
                          `${localName}:${variableName},`
                        );
                        exportContent = exportContent.concat(
                          `${variableName} as ${exportName},`
                        );
                      });
                      magicString.append(
                        `
 const {${deconstructContent.slice(
                          0,
                          deconstructContent.length - 1
                        )}} = ${afterImportName}; 
`
                      );
                      magicString.append(
                        `
 export {${exportContent.slice(
                          0,
                          exportContent.length - 1
                        )}}; `
                      );
                    }
                    break;
                  }
                }
              }
            }
          }
        });
        if (requiresRuntime) {
          let requiresCode = `import {__federation_method_ensure, __federation_method_getRemote , __federation_method_wrapDefault , __federation_method_unwrapDefault} from '__federation__';

`;
          if (manualRequired) {
            requiresCode = `import {__federation_method_setRemote, __federation_method_ensure, __federation_method_getRemote , __federation_method_wrapDefault , __federation_method_unwrapDefault} from '__federation__';

`;
            magicString.overwrite(manualRequired.start, manualRequired.end, ``);
          }
          magicString.prepend(requiresCode);
        }
        if (hasImportShared) {
          magicString.prepend(
            `import {importShared} from '\0virtual:__federation_fn_import';
`
          );
        }
        if (requiresRuntime || hasImportShared || modify) {
          return {
            code: magicString.toString(),
            map: magicString.generateMap({ hires: true })
          };
        }
      }
    },
    generateBundle(options2, bundle) {
      const preloadSharedReg = parsedOptions.prodShared.filter((shareInfo) => shareInfo[1].modulePreload).map(
        (item) => new RegExp(`__federation_shared_${item[0]}-.{8}.js`, "g")
      );
      const getImportedChunks = (chunk, satisfy, seen = /* @__PURE__ */ new Set()) => {
        const chunks = [];
        chunk.imports.forEach((file) => {
          const importee = bundle[file];
          if (importee) {
            if (importee.type === "chunk" && !seen.has(file)) {
              if (satisfy(importee)) {
                seen.add(file);
                chunks.push(...getImportedChunks(importee, satisfy, seen));
                chunks.push(importee);
              }
            }
          }
        });
        return chunks;
      };
      const sharedFiles = [];
      const entryChunk = {};
      for (const fileName in bundle) {
        const file = bundle[fileName];
        if (file.type === "asset") {
          if (fileName.endsWith(".html")) {
            entryChunk[fileName] = file;
          }
        } else {
          if (preloadSharedReg.some((item) => item.test(fileName))) {
            sharedFiles.push(fileName);
          }
        }
      }
      if (!sharedFiles.length)
        return;
      Object.keys(entryChunk).forEach((fileName) => {
        let html = entryChunk[fileName].source;
        const htmlPath = entryChunk[fileName].fileName;
        const basePath = resolvedConfig.base === "./" || resolvedConfig.base === "" ? path.posix.join(
          path.posix.relative(entryChunk[fileName].fileName, "").slice(0, -2),
          "./"
        ) : resolvedConfig.base;
        const toOutputFilePath = (filename) => toOutputFilePathWithoutRuntime(
          filename,
          "asset",
          htmlPath,
          "html",
          resolvedConfig,
          (filename2) => basePath + filename2
        );
        const importFiles = sharedFiles.filter((item) => {
          return !html.includes(toOutputFilePath(item));
        }).flatMap((item) => {
          const filepath = item;
          const importFiles2 = getImportedChunks(
            bundle[item],
            (chunk) => !html.includes(toOutputFilePath(chunk.fileName))
          ).map((item2) => item2.fileName);
          return [filepath, ...importFiles2].map(
            (item2) => toOutputFilePath(item2)
          );
        });
        html = injectToHead(
          html,
          [...new Set(importFiles)].map((item) => toPreloadTag(item))
        );
        entryChunk[fileName].source = html;
      });
    }
  };
}
const federation_fn_import = "import { satisfy } from '__federation_fn_satisfy'\n\nconst currentImports = {}\n\n// eslint-disable-next-line no-undef\nconst moduleMap = __rf_var__moduleMap\nconst moduleCache = Object.create(null)\nasync function importShared(name, shareScope = 'default') {\n  return moduleCache[name]\n    ? new Promise((r) => r(moduleCache[name]))\n    : (await getSharedFromRuntime(name, shareScope)) || getSharedFromLocal(name)\n}\n// eslint-disable-next-line\nasync function __federation_import(name) {\n  currentImports[name] ??= import(name)\n  return currentImports[name]\n}\nasync function getSharedFromRuntime(name, shareScope) {\n  let module = null\n  if (globalThis?.__federation_shared__?.[shareScope]?.[name]) {\n    const versionObj = globalThis.__federation_shared__[shareScope][name]\n    const requiredVersion = moduleMap[name]?.requiredVersion\n    const hasRequiredVersion = !!requiredVersion\n    if (hasRequiredVersion) {\n      const versionKey = Object.keys(versionObj).find((version) =>\n        satisfy(version, requiredVersion)\n      )\n      if (versionKey) {\n        const versionValue = versionObj[versionKey]\n        module = await (await versionValue.get())()\n      } else {\n        console.log(\n          `provider support ${name}(${versionKey}) is not satisfied requiredVersion(\\${moduleMap[name].requiredVersion})`\n        )\n      }\n    } else {\n      const versionKey = Object.keys(versionObj)[0]\n      const versionValue = versionObj[versionKey]\n      module = await (await versionValue.get())()\n    }\n  }\n  if (module) {\n    return flattenModule(module, name)\n  }\n}\nasync function getSharedFromLocal(name) {\n  if (moduleMap[name]?.import) {\n    let module = await (await moduleMap[name].get())()\n    return flattenModule(module, name)\n  } else {\n    console.error(\n      `consumer config import=false,so cant use callback shared module`\n    )\n  }\n}\nfunction flattenModule(module, name) {\n  // use a shared module which export default a function will getting error 'TypeError: xxx is not a function'\n  if (typeof module.default === 'function') {\n    Object.keys(module).forEach((key) => {\n      if (key !== 'default') {\n        module.default[key] = module[key]\n      }\n    })\n    moduleCache[name] = module.default\n    return module.default\n  }\n  if (module.default) module = Object.assign({}, module.default, module)\n  moduleCache[name] = module\n  return module\n}\nexport {\n  importShared,\n  getSharedFromRuntime as importSharedRuntime,\n  getSharedFromLocal as importSharedLocal\n}\n";
const sharedFilePathReg = /__federation_shared_(.+)-.{8}\.js$/;
function prodSharedPlugin(options) {
  parsedOptions.prodShared = parseSharedOptions(options);
  const shareName2Prop = /* @__PURE__ */ new Map();
  parsedOptions.prodShared.forEach(
    (value) => shareName2Prop.set(removeNonRegLetter(value[0], NAME_CHAR_REG), value[1])
  );
  let isHost;
  let isRemote;
  const id2Prop = /* @__PURE__ */ new Map();
  return {
    name: "originjs:shared-production",
    virtualFile: {
      __federation_fn_import: federation_fn_import
    },
    options(inputOptions) {
      var _a;
      isRemote = !!parsedOptions.prodExpose.length;
      isHost = !!parsedOptions.prodRemote.length && !parsedOptions.prodExpose.length;
      if (shareName2Prop.size) {
        inputOptions.external = (_a = inputOptions.external) == null ? void 0 : _a.filter((item) => {
          if (item instanceof RegExp)
            return ![...shareName2Prop.keys()].some((key) => item.test(key));
          return !shareName2Prop.has(removeNonRegLetter(item, NAME_CHAR_REG));
        });
      }
      return inputOptions;
    },
    async buildStart() {
      var _a;
      if (parsedOptions.prodShared.length && isRemote) {
        this.emitFile({
          name: "__federation_fn_import",
          type: "chunk",
          id: "__federation_fn_import",
          preserveSignature: "strict"
        });
      }
      const collectDirFn = (filePath, collect) => {
        const files = fs.readdirSync(filePath);
        files.forEach((name) => {
          const tempPath = path$1.join(filePath, name);
          const isDir = fs.statSync(tempPath).isDirectory();
          if (isDir) {
            collect.push(tempPath);
            collectDirFn(tempPath, collect);
          }
        });
      };
      const monoRepos = [];
      const dirPaths = [];
      const currentDir = path$1.resolve();
      for (const arr of parsedOptions.prodShared) {
        if (isHost && !arr[1].version && !arr[1].manuallyPackagePathSetting) {
          const packageJsonPath = (_a = await this.resolve(`${arr[1].packagePath}/package.json`)) == null ? void 0 : _a.id;
          if (packageJsonPath) {
            const packageJson = JSON.parse(
              fs.readFileSync(packageJsonPath, { encoding: "utf-8" })
            );
            arr[1].version = packageJson.version;
          } else {
            arr[1].removed = true;
            const dir = path$1.join(currentDir, "node_modules", arr[0]);
            const dirStat = fs.statSync(dir);
            if (dirStat.isDirectory()) {
              collectDirFn(dir, dirPaths);
            } else {
              this.error(`cant resolve "${arr[1].packagePath}"`);
            }
            if (dirPaths.length > 0) {
              monoRepos.push({ arr: dirPaths, root: arr });
            }
          }
          if (!arr[1].removed && !arr[1].version) {
            this.error(
              `No description file or no version in description file (usually package.json) of ${arr[0]}. Add version to description file, or manually specify version in shared config.`
            );
          }
        }
      }
      parsedOptions.prodShared = parsedOptions.prodShared.filter(
        (item) => !item[1].removed
      );
      if (monoRepos.length > 0) {
        for (const monoRepo of monoRepos) {
          for (const id of monoRepo.arr) {
            try {
              const idResolve = await this.resolve(id);
              if (idResolve == null ? void 0 : idResolve.id) {
                parsedOptions.prodShared.push([
                  `${monoRepo.root[0]}/${path$1.basename(id)}`,
                  {
                    id: idResolve == null ? void 0 : idResolve.id,
                    import: monoRepo.root[1].import,
                    shareScope: monoRepo.root[1].shareScope,
                    root: monoRepo.root
                  }
                ]);
              }
            } catch (e) {
            }
          }
        }
      }
      if (parsedOptions.prodShared.length && isRemote) {
        for (const prod of parsedOptions.prodShared) {
          id2Prop.set(prod[1].id, prod[1]);
        }
      }
    },
    outputOptions: function(outputOption) {
      outputOption.hoistTransitiveImports = false;
      const manualChunkFunc = (id) => {
        const find = parsedOptions.prodShared.find(
          (arr) => {
            var _a;
            return (_a = arr[1].dependencies) == null ? void 0 : _a.has(id);
          }
        );
        return find ? find[0] : void 0;
      };
      if (typeof outputOption.manualChunks === "function") {
        outputOption.manualChunks = new Proxy(outputOption.manualChunks, {
          apply(target, thisArg, argArray) {
            const result = manualChunkFunc(argArray[0]);
            return result ? result : target(argArray[0], argArray[1]);
          }
        });
      }
      if (outputOption.manualChunks === void 0) {
        outputOption.manualChunks = manualChunkFunc;
      }
      return outputOption;
    },
    generateBundle(options2, bundle) {
      var _a;
      if (!isRemote) {
        return;
      }
      const needRemoveShared = /* @__PURE__ */ new Set();
      for (const key in bundle) {
        const chunk = bundle[key];
        if (chunk.type === "chunk") {
          if (!isHost) {
            const regRst = sharedFilePathReg.exec(chunk.fileName);
            if (regRst && ((_a = shareName2Prop.get(removeNonRegLetter(regRst[1], NAME_CHAR_REG))) == null ? void 0 : _a.generate) === false) {
              needRemoveShared.add(key);
            }
          }
        }
      }
      if (needRemoveShared.size !== 0) {
        for (const key of needRemoveShared) {
          delete bundle[key];
        }
      }
    }
  };
}
function prodExposePlugin(options) {
  let moduleMap = "";
  const hasOptions = parsedOptions.prodExpose.some((expose) => {
    var _a;
    return expose[0] === ((_a = parseExposeOptions(options)[0]) == null ? void 0 : _a[0]);
  });
  if (!hasOptions) {
    parsedOptions.prodExpose = Array.prototype.concat(
      parsedOptions.prodExpose,
      parseExposeOptions(options)
    );
  }
  for (const item of parseExposeOptions(options)) {
    getModuleMarker(`\${${item[0]}}`, SHARED);
    const exposeFilepath = normalizePath(path$1.resolve(item[1].import));
    EXPOSES_MAP.set(item[0], exposeFilepath);
    EXPOSES_KEY_MAP.set(
      item[0],
      `__federation_expose_${removeNonRegLetter(item[0], NAME_CHAR_REG)}`
    );
    moduleMap += `
"${item[0]}":()=>{
      ${DYNAMIC_LOADING_CSS}('${DYNAMIC_LOADING_CSS_PREFIX}${exposeFilepath}', ${item[1].dontAppendStylesToHead}, '${item[0]}')
      return __federation_import('\${__federation_expose_${item[0]}}').then(module =>Object.keys(module).every(item => exportSet.has(item)) ? () => module.default : () => module)},`;
  }
  return {
    name: "originjs:expose-production",
    virtualFile: {
      [`__remoteEntryHelper__${options.filename}`]: `
      const currentImports = {}
      const exportSet = new Set(['Module', '__esModule', 'default', '_export_sfc']);
      let moduleMap = {${moduleMap}}
      const seen = {}
      export const ${DYNAMIC_LOADING_CSS} = (cssFilePaths, dontAppendStylesToHead, exposeItemName) => {
        const metaUrl = import.meta.url;
        if (typeof metaUrl === 'undefined') {
          console.warn('The remote style takes effect only when the build.target option in the vite.config.ts file is higher than that of "es2020".');
          return;
        }

        const curUrl = metaUrl.substring(0, metaUrl.lastIndexOf('${options.filename}'));
        const base = __VITE_BASE_PLACEHOLDER__;
        const assetsDir = __VITE_ASSETS_DIR_PLACEHOLDER__;

        cssFilePaths.forEach(cssPath => {
         let href = '';
         const baseUrl = base || curUrl;
         if (baseUrl) {
           const trimmer = {
             trailing: (path) => (path.endsWith('/') ? path.slice(0, -1) : path),
             leading: (path) => (path.startsWith('/') ? path.slice(1) : path)
           }
           const isAbsoluteUrl = (url) => url.startsWith('http') || url.startsWith('//');

           const cleanBaseUrl = trimmer.trailing(baseUrl);
           const cleanCssPath = trimmer.leading(cssPath);
           const cleanCurUrl = trimmer.trailing(curUrl);

           if (isAbsoluteUrl(baseUrl)) {
             href = [cleanBaseUrl, cleanCssPath].filter(Boolean).join('/');
           } else {
            if (cleanCurUrl.includes(cleanBaseUrl)) {
              href = [cleanCurUrl, cleanCssPath].filter(Boolean).join('/');
            } else {
              href = [cleanCurUrl + cleanBaseUrl, cleanCssPath].filter(Boolean).join('/');
            }
           }
         } else {
           href = cssPath;
         }
         
          if (dontAppendStylesToHead) {
            const key = 'css__${options.name}__' + exposeItemName;
            window[key] = window[key] || [];
            window[key].push(href);
            return;
          }

          if (href in seen) return;
          seen[href] = true;

          const element = document.createElement('link');
          element.rel = 'stylesheet';
          element.href = href;
          document.head.appendChild(element);
        });
      };
      async function __federation_import(name) {
        currentImports[name] ??= import(name)
        return currentImports[name]
      };
      export const get =(module) => {
        if(!moduleMap[module]) throw new Error('Can not find remote module ' + module)
        return moduleMap[module]();
      };
      export const init =(shareScope) => {
        globalThis.__federation_shared__= globalThis.__federation_shared__|| {};
        Object.entries(shareScope).forEach(([key, value]) => {
          for (const [versionKey, versionValue] of Object.entries(value)) {
            const scope = versionValue.scope || 'default'
            globalThis.__federation_shared__[scope] = globalThis.__federation_shared__[scope] || {};
            const shared= globalThis.__federation_shared__[scope];
            (shared[key] = shared[key]||{})[versionKey] = versionValue;
          }
        });
      }`
    },
    configResolved(config) {
      if (config) {
        viteConfigResolved.config = config;
      }
    },
    buildStart() {
      if (parsedOptions.prodExpose.length > 0) {
        this.emitFile({
          fileName: `${builderInfo.assetsDir ? builderInfo.assetsDir + "/" : ""}${options.filename}`,
          type: "chunk",
          id: `__remoteEntryHelper__${options.filename}`,
          preserveSignature: "strict"
        });
      }
    },
    generateBundle(_options, bundle) {
      var _a, _b, _c, _d, _e, _f, _g;
      let remoteEntryChunk;
      for (const file in bundle) {
        const chunk = bundle[file];
        if ((chunk == null ? void 0 : chunk.facadeModuleId) === `\0virtual:__remoteEntryHelper__${options.filename}`) {
          remoteEntryChunk = chunk;
          break;
        }
      }
      if (remoteEntryChunk) {
        remoteEntryChunk.code = remoteEntryChunk.code.replace(
          "__VITE_BASE_PLACEHOLDER__",
          `'${((_a = viteConfigResolved.config) == null ? void 0 : _a.base) || ""}'`
        ).replace(
          "__VITE_ASSETS_DIR_PLACEHOLDER__",
          `'${((_c = (_b = viteConfigResolved.config) == null ? void 0 : _b.build) == null ? void 0 : _c.assetsDir) || ""}'`
        );
        const filepathMap = /* @__PURE__ */ new Map();
        const getFilename = (name) => path$1.parse(path$1.parse(name).name).name;
        const cssBundlesMap = Object.keys(bundle).filter((name) => path$1.extname(name) === ".css").reduce((res, name) => {
          const filename = getFilename(name);
          res.set(filename, bundle[name]);
          return res;
        }, /* @__PURE__ */ new Map());
        remoteEntryChunk.code = remoteEntryChunk.code.replace(
          new RegExp(`(["'])${DYNAMIC_LOADING_CSS_PREFIX}.*?\\1`, "g"),
          (str) => {
            if (viteConfigResolved.config && !viteConfigResolved.config.build.cssCodeSplit) {
              if (cssBundlesMap.size) {
                return `[${[...cssBundlesMap.values()].map(
                  (cssBundle) => JSON.stringify(path$1.basename(cssBundle.fileName))
                ).join(",")}]`;
              } else {
                return "[]";
              }
            }
            const filepath = str.slice(
              (`'` + DYNAMIC_LOADING_CSS_PREFIX).length,
              -1
            );
            if (!filepath || !filepath.length)
              return str;
            let fileBundle = filepathMap.get(filepath);
            if (!fileBundle) {
              fileBundle = Object.values(bundle).find(
                (b) => "facadeModuleId" in b && b.facadeModuleId === filepath
              );
              if (fileBundle)
                filepathMap.set(filepath, fileBundle);
              else
                return str;
            }
            const depCssFiles = /* @__PURE__ */ new Set();
            const addDepCss = (bundleName) => {
              const theBundle = bundle[bundleName];
              if (theBundle && theBundle.viteMetadata) {
                for (const cssFileName of theBundle.viteMetadata.importedCss.values()) {
                  const cssBundle = cssBundlesMap.get(getFilename(cssFileName));
                  if (cssBundle) {
                    depCssFiles.add(cssBundle.fileName);
                  }
                }
              }
              if (theBundle && theBundle.imports && theBundle.imports.length) {
                theBundle.imports.forEach((name) => addDepCss(name));
              }
            };
            [fileBundle.fileName, ...fileBundle.imports].forEach(addDepCss);
            return `[${[...depCssFiles].map((d) => JSON.stringify(path$1.basename(d))).join(",")}]`;
          }
        );
        for (const expose of parseExposeOptions(options)) {
          const module2 = Object.keys(bundle).find((module22) => {
            const chunk = bundle[module22];
            return chunk.name === EXPOSES_KEY_MAP.get(expose[0]);
          });
          if (module2) {
            const chunk = bundle[module2];
            const fileRelativePath = path$1.relative(
              path$1.dirname(remoteEntryChunk.fileName),
              chunk.fileName
            );
            const slashPath = fileRelativePath.replace(/\\/g, "/");
            remoteEntryChunk.code = remoteEntryChunk.code.replace(
              `\${__federation_expose_${expose[0]}}`,
              ((_e = (_d = viteConfigResolved.config) == null ? void 0 : _d.base) == null ? void 0 : _e.replace(/\/+$/, "")) ? [
                viteConfigResolved.config.base.replace(/\/+$/, ""),
                (_g = (_f = viteConfigResolved.config.build) == null ? void 0 : _f.assetsDir) == null ? void 0 : _g.replace(
                  /\/+$/,
                  ""
                ),
                slashPath
              ].filter(Boolean).join("/") : `./${slashPath}`
            );
          }
        }
        let ast = null;
        try {
          ast = this.parse(remoteEntryChunk.code);
        } catch (err) {
          console.error(err);
        }
        if (!ast) {
          return;
        }
        const magicString = new MagicString(remoteEntryChunk.code);
        walk(ast, {
          enter(node) {
            var _a2, _b2;
            if (node && node.type === "CallExpression" && typeof ((_a2 = node.arguments[0]) == null ? void 0 : _a2.value) === "string" && ((_b2 = node.arguments[0]) == null ? void 0 : _b2.value.indexOf(
              `${DYNAMIC_LOADING_CSS_PREFIX}`
            )) > -1) {
              magicString.remove(node.start, node.end + 1);
            }
          }
        });
        remoteEntryChunk.code = magicString.toString();
      }
    }
  };
}
function devSharedPlugin(options) {
  parsedOptions.devShared = parseSharedOptions(options);
  return {
    name: "originjs:shared-development"
  };
}
function devRemotePlugin(options) {
  parsedOptions.devRemote = parseRemoteOptions(options);
  for (const item of parsedOptions.devRemote) {
    devRemotes.push({
      id: item[0],
      regexp: new RegExp(`^${item[0]}/.+?`),
      config: item[1]
    });
  }
  const needHandleFileType = [
    ".js",
    ".ts",
    ".jsx",
    ".tsx",
    ".mjs",
    ".cjs",
    ".vue",
    ".svelte"
  ];
  options.transformFileTypes = (options.transformFileTypes ?? []).concat(needHandleFileType).map((item) => item.toLowerCase());
  const transformFileTypeSet = new Set(options.transformFileTypes);
  let viteDevServer;
  return {
    name: "originjs:remote-development",
    virtualFile: options.remotes ? {
      __federation__: `
${createRemotesMap(devRemotes)}
const loadJS = async (url, fn) => {
  const resolvedUrl = typeof url === 'function' ? await url() : url;
  const script = document.createElement('script')
  script.type = 'text/javascript';
  script.onload = fn;
  script.src = resolvedUrl;
  document.getElementsByTagName('head')[0].appendChild(script);
}
function get(name, ${REMOTE_FROM_PARAMETER}){
  return import(/* @vite-ignore */ name).then(module => ()=> {
    if (${REMOTE_FROM_PARAMETER} === 'webpack') {
      return Object.prototype.toString.call(module).indexOf('Module') > -1 && module.default ? module.default : module
    }
    return module
  })
}
const wrapShareScope = ${REMOTE_FROM_PARAMETER} => {
  return {
    ${getModuleMarker("shareScope")}
  }
}
const initMap = Object.create(null);
async function __federation_method_ensure(remoteId) {
  const remote = remotesMap[remoteId];
  if (!remote.inited) {
    if ('var' === remote.format) {
      // loading js with script tag
      return new Promise(resolve => {
        const callback = () => {
          if (!remote.inited) {
            remote.lib = window[remoteId];
            remote.lib.init(wrapShareScope(remote.from))
            remote.inited = true;
          }
          resolve(remote.lib);
        }
        return loadJS(remote.url, callback);
      });
    } else if (['esm', 'systemjs'].includes(remote.format)) {
      // loading js with import(...)
      return new Promise((resolve, reject) => {
        const getUrl = typeof remote.url === 'function' ? remote.url : () => Promise.resolve(remote.url);
        getUrl().then(url => {
          import(/* @vite-ignore */ url).then(lib => {
            if (!remote.inited) {
              const shareScope = wrapShareScope(remote.from)
              lib.init(shareScope);
              remote.lib = lib;
              remote.lib.init(shareScope);
              remote.inited = true;
            }
            resolve(remote.lib);
          }).catch(reject)
        })
      })
    }
  } else {
    return remote.lib;
  }
}

function __federation_method_unwrapDefault(module) {
  return (module?.__esModule || module?.[Symbol.toStringTag] === 'Module')?module.default:module
}

function __federation_method_wrapDefault(module ,need){
  if (!module?.default && need) {
    let obj = Object.create(null);
    obj.default = module;
    obj.__esModule = true;
    return obj;
  }
  return module; 
}

function __federation_method_getRemote(remoteName,  componentName){
  return __federation_method_ensure(remoteName).then((remote) => remote.get(componentName).then(factory => factory()));
}

function __federation_method_setRemote(remoteName, remoteConfig) {
  remotesMap[remoteName] = remoteConfig;
}
export {__federation_method_ensure, __federation_method_getRemote , __federation_method_setRemote , __federation_method_unwrapDefault , __federation_method_wrapDefault}
;`
    } : { __federation__: "" },
    config(config) {
      if (parsedOptions.devRemote.length) {
        const excludeRemotes = [];
        parsedOptions.devRemote.forEach((item) => excludeRemotes.push(item[0]));
        let optimizeDeps = config.optimizeDeps;
        if (!optimizeDeps) {
          optimizeDeps = config.optimizeDeps = {};
        }
        if (!optimizeDeps.exclude) {
          optimizeDeps.exclude = [];
        }
        optimizeDeps.exclude = optimizeDeps.exclude.concat(excludeRemotes);
      }
    },
    configureServer(server) {
      viteDevServer = server;
    },
    async transform(code, id) {
      var _a;
      if (builderInfo.isHost && !builderInfo.isRemote) {
        for (const arr of parsedOptions.devShared) {
          if (!arr[1].version && !arr[1].manuallyPackagePathSetting) {
            const packageJsonPath = (_a = await this.resolve(`${arr[0]}/package.json`)) == null ? void 0 : _a.id;
            if (!packageJsonPath) {
              this.error(
                `No description file or no version in description file (usually package.json) of ${arr[0]}(${packageJsonPath}). Add version to description file, or manually specify version in shared config.`
              );
            } else {
              const json = JSON.parse(
                fs.readFileSync(packageJsonPath, { encoding: "utf-8" })
              );
              arr[1].version = json.version;
            }
          }
        }
      }
      if (id === "\0virtual:__federation__") {
        const scopeCode = await devSharedScopeCode.call(
          this,
          parsedOptions.devShared
        );
        return code.replace(getModuleMarker("shareScope"), scopeCode.join(","));
      }
      const fileExtname = getFileExtname(id);
      if (!transformFileTypeSet.has((fileExtname ?? "").toLowerCase())) {
        return;
      }
      let ast = null;
      try {
        ast = this.parse(code);
      } catch (err) {
        console.error(err);
      }
      if (!ast) {
        return null;
      }
      const magicString = new MagicString(code);
      const hasStaticImported = /* @__PURE__ */ new Map();
      let requiresRuntime = false;
      let manualRequired = null;
      walk(ast, {
        enter(node) {
          var _a2, _b, _c, _d;
          if (node.type === "ImportDeclaration" && ((_a2 = node.source) == null ? void 0 : _a2.value) === "virtual:__federation__") {
            manualRequired = node;
          }
          if ((node.type === "ImportExpression" || node.type === "ImportDeclaration" || node.type === "ExportNamedDeclaration") && ((_c = (_b = node.source) == null ? void 0 : _b.value) == null ? void 0 : _c.indexOf("/")) > -1) {
            const moduleId = node.source.value;
            const remote = devRemotes.find((r) => r.regexp.test(moduleId));
            const needWrap = (remote == null ? void 0 : remote.config.from) === "vite";
            if (remote) {
              requiresRuntime = true;
              const modName = `.${moduleId.slice(remote.id.length)}`;
              switch (node.type) {
                case "ImportExpression": {
                  magicString.overwrite(
                    node.start,
                    node.end,
                    `__federation_method_getRemote(${JSON.stringify(
                      remote.id
                    )} , ${JSON.stringify(
                      modName
                    )}).then(module=>__federation_method_wrapDefault(module, ${needWrap}))`
                  );
                  break;
                }
                case "ImportDeclaration": {
                  if ((_d = node.specifiers) == null ? void 0 : _d.length) {
                    const afterImportName = `__federation_var_${moduleId.replace(
                      /[@/\\.-]/g,
                      ""
                    )}`;
                    if (!hasStaticImported.has(moduleId)) {
                      magicString.overwrite(
                        node.start,
                        node.end,
                        `const ${afterImportName} = await __federation_method_getRemote(${JSON.stringify(
                          remote.id
                        )} , ${JSON.stringify(modName)});`
                      );
                      hasStaticImported.set(moduleId, afterImportName);
                    }
                    let deconstructStr = "";
                    node.specifiers.forEach((spec) => {
                      if (spec.type === "ImportDefaultSpecifier") {
                        magicString.appendRight(
                          node.end,
                          `
 let ${spec.local.name} = __federation_method_unwrapDefault(${afterImportName}) `
                        );
                      } else if (spec.type === "ImportSpecifier") {
                        const importedName = spec.imported.name;
                        const localName = spec.local.name;
                        deconstructStr += `${importedName === localName ? localName : `${importedName} : ${localName}`},`;
                      } else if (spec.type === "ImportNamespaceSpecifier") {
                        magicString.appendRight(
                          node.end,
                          `let {${spec.local.name}} = ${afterImportName}`
                        );
                      }
                    });
                    if (deconstructStr.length > 0) {
                      magicString.appendRight(
                        node.end,
                        `
 let {${deconstructStr.slice(
                          0,
                          -1
                        )}} = ${afterImportName}`
                      );
                    }
                  }
                  break;
                }
                case "ExportNamedDeclaration": {
                  const afterImportName = `__federation_var_${moduleId.replace(
                    /[@/\\.-]/g,
                    ""
                  )}`;
                  if (!hasStaticImported.has(moduleId)) {
                    hasStaticImported.set(moduleId, afterImportName);
                    magicString.overwrite(
                      node.start,
                      node.end,
                      `const ${afterImportName} = await __federation_method_getRemote(${JSON.stringify(
                        remote.id
                      )} , ${JSON.stringify(modName)});`
                    );
                  }
                  if (node.specifiers.length > 0) {
                    const specifiers = node.specifiers;
                    let exportContent = "";
                    let deconstructContent = "";
                    specifiers.forEach((spec) => {
                      const localName = spec.local.name;
                      const exportName = spec.exported.name;
                      const variableName = `${afterImportName}_${localName}`;
                      deconstructContent = deconstructContent.concat(
                        `${localName}:${variableName},`
                      );
                      exportContent = exportContent.concat(
                        `${variableName} as ${exportName},`
                      );
                    });
                    magicString.append(
                      `
 const {${deconstructContent.slice(
                        0,
                        deconstructContent.length - 1
                      )}} = ${afterImportName}; 
`
                    );
                    magicString.append(
                      `
 export {${exportContent.slice(
                        0,
                        exportContent.length - 1
                      )}}; `
                    );
                  }
                  break;
                }
              }
            }
          }
        }
      });
      if (requiresRuntime) {
        let requiresCode = `import {__federation_method_ensure, __federation_method_getRemote , __federation_method_wrapDefault , __federation_method_unwrapDefault} from '__federation__';

`;
        if (manualRequired) {
          requiresCode = `import {__federation_method_setRemote, __federation_method_ensure, __federation_method_getRemote , __federation_method_wrapDefault , __federation_method_unwrapDefault} from '__federation__';

`;
          magicString.overwrite(manualRequired.start, manualRequired.end, ``);
        }
        magicString.prepend(requiresCode);
      }
      return magicString.toString();
    }
  };
  async function devSharedScopeCode(shared) {
    const res = [];
    if (shared.length) {
      const serverConfiguration = viteDevServer.config.server;
      const base = viteDevServer.config.base;
      const cwdPath = normalizePath(process.cwd());
      for (const item of shared) {
        const moduleInfo = await this.resolve(item[1].packagePath, void 0, {
          skipSelf: true
        });
        if (!moduleInfo)
          continue;
        const moduleFilePath = normalizePath(moduleInfo.id);
        const idx = moduleFilePath.indexOf(cwdPath);
        const relativePath = idx === 0 ? path$1.posix.join(base, moduleFilePath.slice(cwdPath.length)) : null;
        const sharedName = item[0];
        const obj = item[1];
        let str = "";
        if (typeof obj === "object") {
          const origin = serverConfiguration.origin;
          const pathname = relativePath ?? `/@fs/${moduleInfo.id}`;
          const url = origin ? `'${origin}${pathname}'` : `window.location.origin+'${pathname}'`;
          str += `get:()=> get(${url}, ${REMOTE_FROM_PARAMETER})`;
          res.push(`'${sharedName}':{'${obj.version}':{${str}}}`);
        }
      }
    }
    return res;
  }
}
function devExposePlugin(options) {
  parsedOptions.devExpose = parseExposeOptions(options);
  return {
    name: "originjs:expose-development"
  };
}
function federation(options) {
  options.filename = options.filename ? options.filename : DEFAULT_ENTRY_FILENAME;
  let pluginList = [];
  let virtualMod;
  let registerCount = 0;
  function registerPlugins(mode, command) {
    if (mode === "production" || command === "build") {
      pluginList = [
        prodSharedPlugin(options),
        prodExposePlugin(options),
        prodRemotePlugin(options)
      ];
    } else if (mode === "development" || command === "serve") {
      pluginList = [
        devSharedPlugin(options),
        devExposePlugin(options),
        devRemotePlugin(options)
      ];
    } else {
      pluginList = [];
    }
    builderInfo.isHost = !!(parsedOptions.prodRemote.length || parsedOptions.devRemote.length);
    builderInfo.isRemote = !!(parsedOptions.prodExpose.length || parsedOptions.devExpose.length);
    builderInfo.isShared = !!(parsedOptions.prodShared.length || parsedOptions.devShared.length);
    let virtualFiles = {};
    pluginList.forEach((plugin) => {
      if (plugin.virtualFile) {
        virtualFiles = Object.assign(virtualFiles, plugin.virtualFile);
      }
    });
    virtualMod = virtual(virtualFiles);
  }
  return {
    name: "originjs:federation",
    enforce: "post",
    options(_options) {
      var _a;
      if (!registerCount++) {
        registerPlugins(options.mode = options.mode ?? "production", "");
      }
      if (typeof _options.input === "string") {
        _options.input = { index: _options.input };
      }
      _options.external = _options.external || [];
      if (!Array.isArray(_options.external)) {
        _options.external = [_options.external];
      }
      for (const pluginHook of pluginList) {
        (_a = pluginHook.options) == null ? void 0 : _a.call(this, _options);
      }
      return _options;
    },
    config(config, env) {
      var _a, _b;
      options.mode = options.mode ?? env.mode;
      registerPlugins(options.mode, env.command);
      registerCount++;
      for (const pluginHook of pluginList) {
        (_a = pluginHook.config) == null ? void 0 : _a.call(this, config, env);
      }
      builderInfo.builder = "vite";
      builderInfo.assetsDir = ((_b = config == null ? void 0 : config.build) == null ? void 0 : _b.assetsDir) ?? "assets";
    },
    configureServer(server) {
      var _a;
      for (const pluginHook of pluginList) {
        (_a = pluginHook.configureServer) == null ? void 0 : _a.call(this, server);
      }
    },
    configResolved(config) {
      var _a;
      for (const pluginHook of pluginList) {
        (_a = pluginHook.configResolved) == null ? void 0 : _a.call(this, config);
      }
    },
    buildStart(inputOptions) {
      var _a;
      for (const pluginHook of pluginList) {
        (_a = pluginHook.buildStart) == null ? void 0 : _a.call(this, inputOptions);
      }
    },
    async resolveId(...args) {
      var _a;
      const v = virtualMod.resolveId.call(this, ...args);
      if (v) {
        return v;
      }
      if (args[0] === "\0virtual:__federation_fn_import") {
        return {
          id: "\0virtual:__federation_fn_import",
          moduleSideEffects: true
        };
      }
      if (args[0] === "__federation_fn_satisfy") {
        const federationId = (_a = await this.resolve("@originjs/vite-plugin-federation")) == null ? void 0 : _a.id;
        return await this.resolve(`${path$1.dirname(federationId)}/satisfy.mjs`);
      }
      if (args[0] === "virtual:__federation__") {
        return {
          id: "\0virtual:__federation__",
          moduleSideEffects: true
        };
      }
      return null;
    },
    load(...args) {
      const v = virtualMod.load.call(this, ...args);
      if (v) {
        return v;
      }
      return null;
    },
    transform(code, id) {
      var _a;
      for (const pluginHook of pluginList) {
        const result = (_a = pluginHook.transform) == null ? void 0 : _a.call(this, code, id);
        if (result) {
          return result;
        }
      }
      return code;
    },
    moduleParsed(moduleInfo) {
      var _a;
      for (const pluginHook of pluginList) {
        (_a = pluginHook.moduleParsed) == null ? void 0 : _a.call(this, moduleInfo);
      }
    },
    outputOptions(outputOptions) {
      var _a;
      for (const pluginHook of pluginList) {
        (_a = pluginHook.outputOptions) == null ? void 0 : _a.call(this, outputOptions);
      }
      return outputOptions;
    },
    renderChunk(code, chunkInfo, _options) {
      var _a;
      for (const pluginHook of pluginList) {
        const result = (_a = pluginHook.renderChunk) == null ? void 0 : _a.call(
          this,
          code,
          chunkInfo,
          _options
        );
        if (result) {
          return result;
        }
      }
      return null;
    },
    generateBundle: function(_options, bundle, isWrite) {
      var _a;
      for (const pluginHook of pluginList) {
        (_a = pluginHook.generateBundle) == null ? void 0 : _a.call(this, _options, bundle, isWrite);
      }
    }
  };
}
module.exports = federation;
