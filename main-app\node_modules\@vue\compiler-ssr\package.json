{"_from": "@vue/compiler-ssr@3.5.17", "_id": "@vue/compiler-ssr@3.5.17", "_inBundle": false, "_integrity": "sha512-hkDbA0Q20ZzGgpj5uZjb9rBzQtIHLS78mMilwrlpWk2Ep37DYntUz0PonQ6kr113vfOEdM+zTBuJDaceNIW0tQ==", "_location": "/@vue/compiler-ssr", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-ssr@3.5.17", "name": "@vue/compiler-ssr", "escapedName": "@vue%2fcompiler-ssr", "scope": "@vue", "rawSpec": "3.5.17", "saveSpec": null, "fetchSpec": "3.5.17"}, "_requiredBy": ["/@vue/compiler-sfc", "/@vue/server-renderer"], "_resolved": "https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.5.17.tgz", "_shasum": "14ba3b7bba6e0e1fd02002316263165a5d1046c7", "_spec": "@vue/compiler-ssr@3.5.17", "_where": "C:\\Users\\<USER>\\Desktop\\mirca_web\\main-app\\main-app\\node_modules\\@vue\\compiler-sfc", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"prod": false, "formats": ["cjs"]}, "bundleDependencies": false, "dependencies": {"@vue/compiler-dom": "3.5.17", "@vue/shared": "3.5.17"}, "deprecated": false, "description": "@vue/compiler-ssr", "files": ["dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-ssr#readme", "keywords": ["vue"], "license": "MIT", "main": "dist/compiler-ssr.cjs.js", "name": "@vue/compiler-ssr", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-ssr"}, "types": "dist/compiler-ssr.d.ts", "version": "3.5.17"}