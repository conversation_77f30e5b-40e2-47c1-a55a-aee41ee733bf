{"_from": "nanoid@^3.3.11", "_id": "nanoid@3.3.11", "_inBundle": false, "_integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "_location": "/nanoid", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "nanoid@^3.3.11", "name": "nanoid", "escapedName": "nanoid", "rawSpec": "^3.3.11", "saveSpec": null, "fetchSpec": "^3.3.11"}, "_requiredBy": ["/postcss"], "_resolved": "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.11.tgz", "_shasum": "4f4f112cefbe303202f2199838128936266d185b", "_spec": "nanoid@^3.3.11", "_where": "C:\\Users\\<USER>\\Desktop\\mirca_web\\main-app\\main-app\\node_modules\\postcss", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "browser": {"./index.js": "./index.browser.js", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs", "./index.cjs": "./index.browser.cjs"}, "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"react-native": "./index.browser.js", "browser": "./index.browser.js", "require": {"types": "./index.d.cts", "default": "./index.cjs"}, "import": {"types": "./index.d.ts", "default": "./index.js"}, "default": "./index.js"}, "./package.json": "./package.json", "./async/package.json": "./async/package.json", "./async": {"browser": "./async/index.browser.js", "require": {"types": "./index.d.cts", "default": "./async/index.cjs"}, "import": {"types": "./index.d.ts", "default": "./async/index.js"}, "default": "./async/index.js"}, "./non-secure/package.json": "./non-secure/package.json", "./non-secure": {"require": {"types": "./index.d.cts", "default": "./non-secure/index.cjs"}, "import": {"types": "./index.d.ts", "default": "./non-secure/index.js"}, "default": "./non-secure/index.js"}, "./url-alphabet/package.json": "./url-alphabet/package.json", "./url-alphabet": {"require": {"types": "./index.d.cts", "default": "./url-alphabet/index.cjs"}, "import": {"types": "./index.d.ts", "default": "./url-alphabet/index.js"}, "default": "./url-alphabet/index.js"}}, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "homepage": "https://github.com/ai/nanoid#readme", "keywords": ["uuid", "random", "id", "url"], "license": "MIT", "main": "index.cjs", "module": "index.js", "name": "nanoid", "react-native": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ai/nanoid.git"}, "sideEffects": false, "type": "module", "types": "./index.d.ts", "version": "3.3.11"}