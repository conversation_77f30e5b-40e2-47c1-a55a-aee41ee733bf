{"_from": "@babel/helper-string-parser@^7.27.1", "_id": "@babel/helper-string-parser@7.27.1", "_inBundle": false, "_integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==", "_location": "/@babel/helper-string-parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-string-parser@^7.27.1", "name": "@babel/helper-string-parser", "escapedName": "@babel%2fhelper-string-parser", "scope": "@babel", "rawSpec": "^7.27.1", "saveSpec": null, "fetchSpec": "^7.27.1"}, "_requiredBy": ["/@babel/types"], "_resolved": "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "_shasum": "54da796097ab19ce67ed9f88b47bb2ec49367687", "_spec": "@babel/helper-string-parser@^7.27.1", "_where": "C:\\Users\\<USER>\\Desktop\\mirca_web\\main-app\\main-app\\node_modules\\@babel\\types", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A utility package to parse strings", "devDependencies": {"charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-string-parser", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-string-parser"}, "type": "commonjs", "version": "7.27.1"}