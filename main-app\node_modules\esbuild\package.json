{"_from": "esbuild@^0.18.10", "_id": "esbuild@0.18.20", "_inBundle": false, "_integrity": "sha512-ceqxoedUrcayh7Y7ZX6NdbbDzGROiyVBgC4PriJThBKSVPWnnFHZAkfI1lJT8QFkOwH4qOS2SJkS4wvpGl8BpA==", "_location": "/esbuild", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "esbuild@^0.18.10", "name": "esbuild", "escapedName": "esbuild", "rawSpec": "^0.18.10", "saveSpec": null, "fetchSpec": "^0.18.10"}, "_requiredBy": ["/vite"], "_resolved": "https://registry.npmmirror.com/esbuild/-/esbuild-0.18.20.tgz", "_shasum": "4709f5a34801b43b799ab7d6d82f7284a9b7a7a6", "_spec": "esbuild@^0.18.10", "_where": "C:\\Users\\<USER>\\Desktop\\mirca_web\\main-app\\main-app\\node_modules\\vite", "bin": {"esbuild": "bin/esbuild"}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "bundleDependencies": false, "dependencies": {"@esbuild/android-arm": "0.18.20", "@esbuild/android-arm64": "0.18.20", "@esbuild/android-x64": "0.18.20", "@esbuild/darwin-arm64": "0.18.20", "@esbuild/darwin-x64": "0.18.20", "@esbuild/freebsd-arm64": "0.18.20", "@esbuild/freebsd-x64": "0.18.20", "@esbuild/linux-arm": "0.18.20", "@esbuild/linux-arm64": "0.18.20", "@esbuild/linux-ia32": "0.18.20", "@esbuild/linux-loong64": "0.18.20", "@esbuild/linux-mips64el": "0.18.20", "@esbuild/linux-ppc64": "0.18.20", "@esbuild/linux-riscv64": "0.18.20", "@esbuild/linux-s390x": "0.18.20", "@esbuild/linux-x64": "0.18.20", "@esbuild/netbsd-x64": "0.18.20", "@esbuild/openbsd-x64": "0.18.20", "@esbuild/sunos-x64": "0.18.20", "@esbuild/win32-arm64": "0.18.20", "@esbuild/win32-ia32": "0.18.20", "@esbuild/win32-x64": "0.18.20"}, "deprecated": false, "description": "An extremely fast JavaScript and CSS bundler and minifier.", "engines": {"node": ">=12"}, "homepage": "https://github.com/evanw/esbuild#readme", "license": "MIT", "main": "lib/main.js", "name": "esbuild", "optionalDependencies": {"@esbuild/android-arm": "0.18.20", "@esbuild/android-arm64": "0.18.20", "@esbuild/android-x64": "0.18.20", "@esbuild/darwin-arm64": "0.18.20", "@esbuild/darwin-x64": "0.18.20", "@esbuild/freebsd-arm64": "0.18.20", "@esbuild/freebsd-x64": "0.18.20", "@esbuild/linux-arm": "0.18.20", "@esbuild/linux-arm64": "0.18.20", "@esbuild/linux-ia32": "0.18.20", "@esbuild/linux-loong64": "0.18.20", "@esbuild/linux-mips64el": "0.18.20", "@esbuild/linux-ppc64": "0.18.20", "@esbuild/linux-riscv64": "0.18.20", "@esbuild/linux-s390x": "0.18.20", "@esbuild/linux-x64": "0.18.20", "@esbuild/netbsd-x64": "0.18.20", "@esbuild/openbsd-x64": "0.18.20", "@esbuild/sunos-x64": "0.18.20", "@esbuild/win32-arm64": "0.18.20", "@esbuild/win32-ia32": "0.18.20", "@esbuild/win32-x64": "0.18.20"}, "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "scripts": {"postinstall": "node install.js"}, "types": "lib/main.d.ts", "version": "0.18.20"}