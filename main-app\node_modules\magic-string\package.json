{"_from": "magic-string@^0.30.17", "_id": "magic-string@0.30.17", "_inBundle": false, "_integrity": "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==", "_location": "/magic-string", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "magic-string@^0.30.17", "name": "magic-string", "escapedName": "magic-string", "rawSpec": "^0.30.17", "saveSpec": null, "fetchSpec": "^0.30.17"}, "_requiredBy": ["/@vue/compiler-sfc"], "_resolved": "https://registry.npmmirror.com/magic-string/-/magic-string-0.30.17.tgz", "_shasum": "450a449673d2460e5bbcfba9a61916a1714c7453", "_spec": "magic-string@^0.30.17", "_where": "C:\\Users\\<USER>\\Desktop\\mirca_web\\main-app\\main-app\\node_modules\\@vue\\compiler-sfc", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "bundleDependencies": false, "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}, "deprecated": false, "description": "Modify strings, generate sourcemaps", "devDependencies": {"@eslint/js": "^9.16.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-replace": "^5.0.7", "benchmark": "^2.1.4", "bumpp": "^9.9.1", "conventional-changelog-cli": "^3.0.0", "eslint": "^9.16.0", "prettier": "^3.4.2", "publint": "^0.2.12", "rollup": "^3.29.5", "source-map-js": "^1.2.1", "source-map-support": "^0.5.21", "vitest": "^2.1.8"}, "exports": {"./package.json": "./package.json", ".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js"}}, "files": ["dist/*", "index.d.ts", "README.md"], "homepage": "https://github.com/rich-harris/magic-string#readme", "jsnext:main": "./dist/magic-string.es.mjs", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "license": "MIT", "main": "./dist/magic-string.cjs.js", "module": "./dist/magic-string.es.mjs", "name": "magic-string", "repository": {"type": "git", "url": "git+https://github.com/rich-harris/magic-string.git"}, "scripts": {"bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "format": "prettier --single-quote --print-width 100 --use-tabs --write .", "lint": "eslint src test && publint", "lint:fix": "eslint src test --fix", "pretest": "npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "test": "vitest run", "test:dev": "vitest", "watch": "rollup -cw"}, "sideEffects": false, "types": "./dist/magic-string.cjs.d.ts", "version": "0.30.17"}