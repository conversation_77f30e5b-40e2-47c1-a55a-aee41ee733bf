{"_from": "entities@^4.5.0", "_id": "entities@4.5.0", "_inBundle": false, "_integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==", "_location": "/entities", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "entities@^4.5.0", "name": "entities", "escapedName": "entities", "rawSpec": "^4.5.0", "saveSpec": null, "fetchSpec": "^4.5.0"}, "_requiredBy": ["/@vue/compiler-core"], "_resolved": "https://registry.npmmirror.com/entities/-/entities-4.5.0.tgz", "_shasum": "5d268ea5e7113ec74c4d033b79ea5a35a488fb48", "_spec": "entities@^4.5.0", "_where": "C:\\Users\\<USER>\\Desktop\\mirca_web\\main-app\\main-app\\node_modules\\@vue\\compiler-core", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/entities/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Encode & decode XML and HTML entities with ease & speed", "devDependencies": {"@types/jest": "^28.1.8", "@types/node": "^18.15.11", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-node": "^11.1.0", "jest": "^28.1.3", "prettier": "^2.8.7", "ts-jest": "^28.0.8", "typedoc": "^0.24.1", "typescript": "^5.0.4"}, "directories": {"lib": "lib/"}, "engines": {"node": ">=0.12"}, "exports": {".": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "./lib/decode.js": {"require": "./lib/decode.js", "import": "./lib/esm/decode.js"}, "./lib/escape.js": {"require": "./lib/escape.js", "import": "./lib/esm/escape.js"}}, "files": ["lib/**/*"], "funding": "https://github.com/fb55/entities?sponsor=1", "homepage": "https://github.com/fb55/entities#readme", "jest": {"preset": "ts-jest", "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "keywords": ["entity", "decoding", "encoding", "html", "xml", "html entities"], "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "module": "lib/esm/index.js", "name": "entities", "prettier": {"tabWidth": 4, "proseWrap": "always"}, "repository": {"type": "git", "url": "git://github.com/fb55/entities.git"}, "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/fb55/entities/$(git rev-parse HEAD)/src/", "build:docs": "typedoc --hideGenerator src/index.ts", "build:encode-trie": "ts-node scripts/write-encode-map.ts", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "build:trie": "ts-node scripts/write-decode-map.ts", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint .", "lint:prettier": "npm run prettier -- --check", "prepare": "npm run build", "prettier": "prettier '**/*.{ts,md,json,yml}'", "test": "npm run test:jest && npm run lint", "test:jest": "jest"}, "sideEffects": false, "types": "lib/index.d.ts", "version": "4.5.0"}