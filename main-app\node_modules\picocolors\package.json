{"_from": "picocolors@^1.1.1", "_id": "picocolors@1.1.1", "_inBundle": false, "_integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "_location": "/picocolors", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "picocolors@^1.1.1", "name": "picocolors", "escapedName": "picocolors", "rawSpec": "^1.1.1", "saveSpec": null, "fetchSpec": "^1.1.1"}, "_requiredBy": ["/postcss"], "_resolved": "https://registry.npmmirror.com/picocolors/-/picocolors-1.1.1.tgz", "_shasum": "3d321af3eab939b083c8f929a1d12cda81c26b6b", "_spec": "picocolors@^1.1.1", "_where": "C:\\Users\\<USER>\\Desktop\\mirca_web\\main-app\\main-app\\node_modules\\postcss", "author": {"name": "<PERSON><PERSON>"}, "browser": {"./picocolors.js": "./picocolors.browser.js"}, "bugs": {"url": "https://github.com/alexeyraspopov/picocolors/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The tiniest and the fastest library for terminal output formatting with ANSI colors", "files": ["picocolors.*", "types.d.ts"], "homepage": "https://github.com/alexeyraspopov/picocolors#readme", "keywords": ["terminal", "colors", "formatting", "cli", "console"], "license": "ISC", "main": "./picocolors.js", "name": "picocolors", "repository": {"type": "git", "url": "git+https://github.com/alexeyraspopov/picocolors.git"}, "sideEffects": false, "types": "./picocolors.d.ts", "version": "1.1.1"}