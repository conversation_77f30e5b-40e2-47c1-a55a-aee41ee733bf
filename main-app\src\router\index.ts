import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/react',
    name: 'ReactApp',
    component: () => (window as any).reactApp.get('./App').then((factory: any) => factory().default)
  },
  {
    path: '/vue',
    name: 'VueApp',
    component: () => (window as any).vueApp.get('./App').then((factory: any) => factory().default)
  },
  {
    path: '/three',
    name: 'ThreeApp',
    component: () => (window as any).threeApp.get('./App').then((factory: any) => factory().default)
  },
  {
    path: '/monitor',
    name: 'MonitorApp',
    component: () => (window as any).monitorApp.get('./App').then((factory: any) => factory().default)
  },
  {
    path: '/visual',
    name: 'VisualApp',
    component: () => (window as any).visualApp.get('./App').then((factory: any) => factory().default)
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router 