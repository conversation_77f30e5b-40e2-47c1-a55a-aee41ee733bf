{"_from": "@originjs/vite-plugin-federation@1.4.1", "_id": "@originjs/vite-plugin-federation@1.4.1", "_inBundle": false, "_integrity": "sha512-Uo08jW5pj1t58OUKuZNkmzcfTN2pqeVuAWCCiKf/75/oll4Efq4cHOqSE1FXMlvwZNGDziNdDyBbQ5IANem3CQ==", "_location": "/@originjs/vite-plugin-federation", "_phantomChildren": {"@jridgewell/sourcemap-codec": "1.5.0", "@types/estree": "1.0.8"}, "_requested": {"type": "version", "registry": true, "raw": "@originjs/vite-plugin-federation@1.4.1", "name": "@originjs/vite-plugin-federation", "escapedName": "@originjs%2fvite-plugin-federation", "scope": "@originjs", "rawSpec": "1.4.1", "saveSpec": null, "fetchSpec": "1.4.1"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmmirror.com/@originjs/vite-plugin-federation/-/vite-plugin-federation-1.4.1.tgz", "_shasum": "e6abc8f18f2cf82783eb87853f4d03e6358b43c2", "_spec": "@originjs/vite-plugin-federation@1.4.1", "_where": "C:\\Users\\<USER>\\Desktop\\mirca_web\\main-app\\main-app", "author": {"name": "@originjs"}, "bugs": {"url": "https://github.com/originjs/vite-plugin-federation/issues"}, "bundleDependencies": false, "dependencies": {"estree-walker": "^3.0.2", "magic-string": "^0.27.0"}, "deprecated": false, "description": "A Vite plugin which support Module Federation.", "devDependencies": {"@rollup/plugin-virtual": "^3.0.1", "@types/estree": "^0.0.50", "conventional-changelog-cli": "^4.1.0", "typescript": "^4.9.4", "vite": "^4.0.5"}, "engines": {"node": ">=14.0.0", "pnpm": ">=7.0.1"}, "exports": {".": {"types": "./types/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "files": ["dist", "types", "README.md", "LICENSE"], "homepage": "https://github.com/originjs/vite-plugin-federation#readme", "keywords": ["vite", "plugins"], "license": "MulanPSL-2.0", "main": "./dist/index.js", "module": "./dist/index.mjs", "name": "@originjs/vite-plugin-federation", "repository": {"type": "git", "url": "git+https://github.com/originjs/vite-plugin-federation.git"}, "scripts": {"build": "vite build", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path .", "dev": "vite build --watch"}, "types": "./types/index.d.ts", "version": "1.4.1"}