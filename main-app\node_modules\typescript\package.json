{"_from": "typescript@^5.0.0", "_id": "typescript@5.8.3", "_inBundle": false, "_integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==", "_location": "/typescript", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "typescript@^5.0.0", "name": "typescript", "escapedName": "typescript", "rawSpec": "^5.0.0", "saveSpec": null, "fetchSpec": "^5.0.0"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmmirror.com/typescript/-/typescript-5.8.3.tgz", "_shasum": "92f8a3e5e3cf497356f4178c34cd65a7f5e8440e", "_spec": "typescript@^5.0.0", "_where": "C:\\Users\\<USER>\\Desktop\\mirca_web\\main-app\\main-app", "author": {"name": "Microsoft Corp."}, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "browser": {"fs": false, "os": false, "path": false, "crypto": false, "buffer": false, "source-map-support": false, "inspector": false, "perf_hooks": false}, "bugs": {"url": "https://github.com/microsoft/TypeScript/issues"}, "bundleDependencies": false, "deprecated": false, "description": "TypeScript is a language for application scale JavaScript development", "devDependencies": {"@dprint/formatter": "^0.4.1", "@dprint/typescript": "0.93.3", "@esfx/canceltoken": "^1.0.0", "@eslint/js": "^9.17.0", "@octokit/rest": "^21.0.2", "@types/chai": "^4.3.20", "@types/diff": "^5.2.3", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.10", "@types/ms": "^0.7.34", "@types/node": "latest", "@types/source-map-support": "^0.5.10", "@types/which": "^3.0.4", "@typescript-eslint/rule-tester": "^8.18.1", "@typescript-eslint/type-utils": "^8.18.1", "@typescript-eslint/utils": "^8.18.1", "azure-devops-node-api": "^14.1.0", "c8": "^10.1.3", "chai": "^4.5.0", "chalk": "^4.1.2", "chokidar": "^3.6.0", "diff": "^5.2.0", "dprint": "^0.47.6", "esbuild": "^0.24.0", "eslint": "^9.17.0", "eslint-formatter-autolinkable-stylish": "^1.4.0", "eslint-plugin-regexp": "^2.7.0", "fast-xml-parser": "^4.5.1", "glob": "^10.4.5", "globals": "^15.13.0", "hereby": "^1.10.0", "jsonc-parser": "^3.3.1", "knip": "^5.41.0", "minimist": "^1.2.8", "mocha": "^10.8.2", "mocha-fivemat-progress-reporter": "^0.1.0", "monocart-coverage-reports": "^2.11.4", "ms": "^2.1.3", "playwright": "^1.49.1", "source-map-support": "^0.5.21", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1", "which": "^3.0.1"}, "engines": {"node": ">=14.17"}, "files": ["bin", "lib", "!lib/enu", "LICENSE.txt", "README.md", "SECURITY.md", "ThirdPartyNoticeText.txt", "!**/.gitattributes"], "gitHead": "68cead182cc24afdc3f1ce7c8ff5853aba14b65a", "homepage": "https://www.typescriptlang.org/", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript"], "license": "Apache-2.0", "main": "./lib/typescript.js", "name": "typescript", "overrides": {"typescript@*": "$typescript"}, "packageManager": "npm@8.19.4", "repository": {"type": "git", "url": "git+https://github.com/microsoft/TypeScript.git"}, "scripts": {"build": "npm run build:compiler && npm run build:tests", "build:compiler": "hereby local", "build:tests": "hereby tests", "build:tests:notypecheck": "hereby tests --no-typecheck", "clean": "hereby clean", "format": "dprint fmt", "gulp": "hereby", "knip": "hereby knip", "lint": "hereby lint", "setup-hooks": "node scripts/link-hooks.mjs", "test": "hereby runtests-parallel --light=false", "test:eslint-rules": "hereby run-eslint-rules-tests"}, "typings": "./lib/typescript.d.ts", "version": "5.8.3", "volta": {"node": "20.1.0", "npm": "8.19.4"}}