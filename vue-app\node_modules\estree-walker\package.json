{"_from": "estree-walker@^2.0.2", "_id": "estree-walker@2.0.2", "_inBundle": false, "_integrity": "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==", "_location": "/estree-walker", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "estree-walker@^2.0.2", "name": "estree-walker", "escapedName": "estree-walker", "rawSpec": "^2.0.2", "saveSpec": null, "fetchSpec": "^2.0.2"}, "_requiredBy": ["/@vue/compiler-core", "/@vue/compiler-sfc"], "_resolved": "https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz", "_shasum": "52f010178c2a4c117a7757cfe942adb7d2da4cac", "_spec": "estree-walker@^2.0.2", "_where": "C:\\Users\\<USER>\\Desktop\\mirca_web\\main-app\\vue-app\\node_modules\\@vue\\compiler-core", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/<PERSON>-<PERSON>/estree-walker/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Traverse an ESTree-compliant AST", "devDependencies": {"@types/estree": "0.0.42", "rollup": "^2.10.9", "typescript": "^3.7.5", "uvu": "^0.5.1"}, "exports": {"require": "./dist/umd/estree-walker.js", "import": "./dist/esm/estree-walker.js"}, "files": ["src", "dist", "types", "README.md"], "homepage": "https://github.com/<PERSON>-<PERSON>/estree-walker#readme", "license": "MIT", "main": "./dist/umd/estree-walker.js", "module": "./dist/esm/estree-walker.js", "name": "estree-walker", "private": false, "repository": {"type": "git", "url": "git+https://github.com/<PERSON>-<PERSON>/estree-walker.git"}, "scripts": {"build": "tsc && rollup -c", "prepublishOnly": "npm run build && npm test", "test": "uvu test"}, "type": "commonjs", "types": "types/index.d.ts", "version": "2.0.2"}