{"_from": "magic-string@^0.27.0", "_id": "magic-string@0.27.0", "_inBundle": false, "_integrity": "sha512-8UnnX2PeRAPZuN12svgR9j7M1uWMovg/CEnIwIG0LFkXSJJe4PdfUGiTGl8V9bsBHFUtfVINcSyYxd7q+kx9fA==", "_location": "/@originjs/vite-plugin-federation/magic-string", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "magic-string@^0.27.0", "name": "magic-string", "escapedName": "magic-string", "rawSpec": "^0.27.0", "saveSpec": null, "fetchSpec": "^0.27.0"}, "_requiredBy": ["/@originjs/vite-plugin-federation"], "_resolved": "https://registry.npmmirror.com/magic-string/-/magic-string-0.27.0.tgz", "_shasum": "e4a3413b4bab6d98d2becffd48b4a257effdbbf3", "_spec": "magic-string@^0.27.0", "_where": "C:\\Users\\<USER>\\Desktop\\mirca_web\\main-app\\main-app\\node_modules\\@originjs\\vite-plugin-federation", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/rich-harris/magic-string/issues"}, "bundleDependencies": false, "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.13"}, "deprecated": false, "description": "Modify strings, generate sourcemaps", "devDependencies": {"@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-replace": "^4.0.0", "benchmark": "^2.1.4", "bumpp": "^8.2.1", "conventional-changelog-cli": "^2.2.2", "eslint": "^8.23.1", "mocha": "^10.0.0", "prettier": "^2.7.1", "rollup": "^2.79.1", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21"}, "engines": {"node": ">=12"}, "exports": {"./package.json": "./package.json", ".": {"import": "./dist/magic-string.es.mjs", "require": "./dist/magic-string.cjs.js", "types": "./index.d.ts"}}, "files": ["dist/*", "index.d.ts", "README.md"], "homepage": "https://github.com/rich-harris/magic-string#readme", "jsnext:main": "./dist/magic-string.es.mjs", "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "license": "MIT", "main": "./dist/magic-string.cjs.js", "module": "./dist/magic-string.es.mjs", "name": "magic-string", "repository": {"type": "git", "url": "git+https://github.com/rich-harris/magic-string.git"}, "scripts": {"bench": "npm run build && node benchmark/index.mjs", "build": "rollup -c", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "lint": "eslint src test", "prepare": "npm run build", "prepublishOnly": "rm -rf dist && npm test", "pretest": "npm run lint && npm run build", "release": "bumpp -x \"npm run changelog\" --all --commit --tag --push && npm publish", "test": "mocha", "watch": "rollup -cw"}, "types": "./index.d.ts", "version": "0.27.0"}