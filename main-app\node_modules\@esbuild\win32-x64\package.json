{"_from": "@esbuild/win32-x64@0.18.20", "_id": "@esbuild/win32-x64@0.18.20", "_inBundle": false, "_integrity": "sha512-kTdfRcSiDfQca/y9QIkng02avJ+NCaQvrMejlsB3RRv5sE9rRoeBPISaZpKxHELzRxZyLvNts1P27W3wV+8geQ==", "_location": "/@esbuild/win32-x64", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@esbuild/win32-x64@0.18.20", "name": "@esbuild/win32-x64", "escapedName": "@esbuild%2fwin32-x64", "scope": "@esbuild", "rawSpec": "0.18.20", "saveSpec": null, "fetchSpec": "0.18.20"}, "_requiredBy": ["/esbuild"], "_resolved": "https://registry.npmmirror.com/@esbuild/win32-x64/-/win32-x64-0.18.20.tgz", "_shasum": "786c5f41f043b07afb1af37683d7c33668858f6d", "_spec": "@esbuild/win32-x64@0.18.20", "_where": "C:\\Users\\<USER>\\Desktop\\mirca_web\\main-app\\main-app\\node_modules\\esbuild", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "bundleDependencies": false, "cpu": ["x64"], "deprecated": false, "description": "The Windows 64-bit binary for esbuild, a JavaScript bundler.", "engines": {"node": ">=12"}, "homepage": "https://github.com/evanw/esbuild#readme", "license": "MIT", "name": "@esbuild/win32-x64", "os": ["win32"], "preferUnplugged": true, "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "version": "0.18.20"}