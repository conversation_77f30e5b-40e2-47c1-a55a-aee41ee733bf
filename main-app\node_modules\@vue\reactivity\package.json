{"_from": "@vue/reactivity@3.5.17", "_id": "@vue/reactivity@3.5.17", "_inBundle": false, "_integrity": "sha512-l/rmw2STIscWi7SNJp708FK4Kofs97zc/5aEPQh4bOsReD/8ICuBcEmS7KGwDj5ODQLYWVN2lNibKJL1z5b+Lw==", "_location": "/@vue/reactivity", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/reactivity@3.5.17", "name": "@vue/reactivity", "escapedName": "@vue%2freactivity", "scope": "@vue", "rawSpec": "3.5.17", "saveSpec": null, "fetchSpec": "3.5.17"}, "_requiredBy": ["/@vue/runtime-core", "/@vue/runtime-dom"], "_resolved": "https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.5.17.tgz", "_shasum": "169b5dcf96c7f23788e5ed9745ec8a7227f2125e", "_spec": "@vue/reactivity@3.5.17", "_where": "C:\\Users\\<USER>\\Desktop\\mirca_web\\main-app\\main-app\\node_modules\\@vue\\runtime-dom", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueReactivity", "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "bundleDependencies": false, "dependencies": {"@vue/shared": "3.5.17"}, "deprecated": false, "description": "@vue/reactivity", "exports": {".": {"types": "./dist/reactivity.d.ts", "node": {"production": "./dist/reactivity.cjs.prod.js", "development": "./dist/reactivity.cjs.js", "default": "./index.js"}, "module": "./dist/reactivity.esm-bundler.js", "import": "./dist/reactivity.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/reactivity#readme", "jsdelivr": "dist/reactivity.global.js", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/reactivity.esm-bundler.js", "name": "@vue/reactivity", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/reactivity"}, "sideEffects": false, "types": "dist/reactivity.d.ts", "unpkg": "dist/reactivity.global.js", "version": "3.5.17"}